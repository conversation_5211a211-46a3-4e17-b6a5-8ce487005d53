# 端口号
# PORT=3000
# 前端基础URL
# FRONTEND_BASE_URL=https://your-frontend-url.com


# 调试相关配置
# 启用pprof
# ENABLE_PPROF=true
# 启用调试模式
# DEBUG=true

# 数据库相关配置
# 数据库连接字符串
# SQL_DSN=user:password@tcp(127.0.0.1:3306)/dbname?parseTime=true
# 日志数据库连接字符串
# LOG_SQL_DSN=user:password@tcp(127.0.0.1:3306)/logdb?parseTime=true
# SQLite数据库路径
# SQLITE_PATH=/path/to/sqlite.db
# 数据库最大空闲连接数
# SQL_MAX_IDLE_CONNS=100
# 数据库最大打开连接数
# SQL_MAX_OPEN_CONNS=1000
# 数据库连接最大生命周期（秒）
# SQL_MAX_LIFETIME=60


# 缓存相关配置
# Redis连接字符串
# REDIS_CONN_STRING=redis://user:password@localhost:6379/0
# 同步频率（单位：秒）
# SYNC_FREQUENCY=60
# 内存缓存启用
# MEMORY_CACHE_ENABLED=true
# 渠道更新频率（单位：秒）
# CHANNEL_UPDATE_FREQUENCY=30
# 批量更新启用
# BATCH_UPDATE_ENABLED=true
# 批量更新间隔（单位：秒）
# BATCH_UPDATE_INTERVAL=5

# 任务和功能配置
# 更新任务启用
# UPDATE_TASK=true

# 对话超时设置
# 所有请求超时时间，单位秒，默认为0，表示不限制
# RELAY_TIMEOUT=0
# 流模式无响应超时时间，单位秒，如果出现空补全可以尝试改为更大值
# STREAMING_TIMEOUT=120

# Gemini 识别图片 最大图片数量
# GEMINI_VISION_MAX_IMAGE_NUM=16

# 会话密钥
# SESSION_SECRET=random_string

# 其他配置
# 渠道测试频率（单位：秒）
# CHANNEL_TEST_FREQUENCY=10
# 生成默认token
# GENERATE_DEFAULT_TOKEN=false
# Cohere 安全设置
# COHERE_SAFETY_SETTING=NONE
# 是否统计图片token
# GET_MEDIA_TOKEN=true
# 是否在非流（stream=false）情况下统计图片token
# GET_MEDIA_TOKEN_NOT_STREAM=true
# 设置 Dify 渠道是否输出工作流和节点信息到客户端
# DIFY_DEBUG=true


# 节点类型
# 如果是主节点则为master
# NODE_TYPE=master

# 日志数据记录配置（新增功能）
# 是否记录用户请求数据（默认false，建议生产环境关闭）
# RECORD_PROMPT_DATA=false
# 是否记录模型响应数据（默认false，建议生产环境关闭）
# RECORD_COMPLETION_DATA=false
# 请求数据最大长度（字节，默认10240=10KB）
# MAX_PROMPT_DATA_LENGTH=10240
# 响应数据最大长度（字节，默认10240=10KB）
# MAX_COMPLETION_DATA_LENGTH=10240

# 注意事项：
# 1. RECORD_PROMPT_DATA 和 RECORD_COMPLETION_DATA 会记录敏感数据，请谨慎启用
# 2. 启用这些功能会显著增加数据库存储空间
# 3. 建议仅在调试或审计需要时启用
# 4. 生产环境建议设置较小的长度限制以控制存储成本
