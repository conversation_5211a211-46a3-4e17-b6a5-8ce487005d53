# 1. 进入 web 目录构建前端
Write-Host "Changing directory to 'web' and building frontend..."
cd web
bun run build
if ($LASTEXITCODE -ne 0) {
    Write-Error "Frontend build failed!"
    exit 1
}
Write-Host "Frontend build successful."

# 2. 返回根目录
Write-Host "Returning to the root directory..."
cd ..

# 3. 编译 Go 应用
Write-Host "Building Go application for linux (64-bit)..."
$env:GOOS = "linux"
$env:GOARCH = "amd64"
go build -o myapp
if ($LASTEXITCODE -ne 0) {
    Write-Error "Go build failed!"
    exit 1
}
Write-Host "Go application build successful. Output: myapp"

# 4. 打包文件
Write-Host "Compressing build artifacts..."
$sourcePaths = @(
    ".\web\dist",
    ".\myapp"
)
$destinationPath = "release.zip"

if (Test-Path $destinationPath) {
    Remove-Item $destinationPath
    Write-Host "Removed existing archive: $destinationPath"
}

Compress-Archive -Path $sourcePaths -DestinationPath $destinationPath -Force
if ($?) {
    Write-Host "Successfully created archive: $destinationPath"
} else {
    Write-Error "Failed to create archive!"
    exit 1
}

Write-Host "<PERSON>ript finished successfully."