package claude

import (
	"bufio"
	"encoding/json"
	"io"
	"net/http"
	"one-api/common"
	"one-api/dto"
	"one-api/service"
	"one-api/types"

	"github.com/gin-gonic/gin"
)

func ClaudeNativeHandler(c *gin.Context, resp *http.Response) (*dto.Usage, *types.NewAPIError) {
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, service.RelayErrorHandler(resp, false)
	}
	common.CloseResponseBodyGracefully(resp)

	var claudeResponse dto.ClaudeResponse
	err = json.Unmarshal(responseBody, &claudeResponse)
	if err != nil {
		return nil, types.NewError(err, types.ErrorCodeBadResponseBody)
	}

	usage := &dto.Usage{
		PromptTokens:     claudeResponse.Usage.InputTokens,
		CompletionTokens: claudeResponse.Usage.OutputTokens,
		TotalTokens:      claudeResponse.Usage.InputTokens + claudeResponse.Usage.OutputTokens,
	}

	c.<PERSON>.Header().Set("Content-Type", "application/json")
	c.Writer.WriteHeader(resp.StatusCode)
	c.Writer.Write(responseBody)

	return usage, nil
}

func ClaudeNativeStreamHandler(c *gin.Context, resp *http.Response) (*dto.Usage, *types.NewAPIError) {
	usage := &dto.Usage{}
	scanner := bufio.NewScanner(resp.Body)
	scanner.Split(bufio.ScanLines)

	for scanner.Scan() {
		data := scanner.Text()
		if len(data) < 6 {
			continue
		}

		data = data[6:]
		if data == "[DONE]" {
			break
		}

		var claudeResponse dto.ClaudeStreamResponse
		err := json.Unmarshal([]byte(data), &claudeResponse)
		if err != nil {
			common.LogError(c, "error unmarshalling stream response: "+err.Error())
			continue
		}

		if claudeResponse.Type == "message_delta" {
			usage.CompletionTokens += claudeResponse.Delta.Usage.OutputTokens
		} else if claudeResponse.Type == "message_start" {
			usage.PromptTokens += claudeResponse.Message.Usage.InputTokens
		}

		c.Writer.Write([]byte("data: " + data + "\n\n"))
		c.Writer.Flush()
	}

	if err := scanner.Err(); err != nil {
		common.LogError(c, "issue reading response stream: "+err.Error())
	}

	common.CloseResponseBodyGracefully(resp)
	usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens

	return usage, nil
}
