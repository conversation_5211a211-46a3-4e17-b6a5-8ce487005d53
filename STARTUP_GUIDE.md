# New API 启动指南

本指南将帮助您快速启动和运行New API项目。

## 系统要求

- Go 1.23.4 或更高版本
- Bun 1.0 或更高版本（用于前端开发）
- Node.js 16 或更高版本（可选，用于某些工具）
- SQLite（默认数据库）/ MySQL 5.7.8+ / PostgreSQL 9.6+（可选）

## 快速启动

### 1. 克隆项目

```bash
git clone https://github.com/Calcium-Ion/new-api.git
cd new-api
```

### 2. 配置环境变量

```bash
# 复制示例环境变量文件
cp .env.example .env

# 编辑环境变量（根据需要）
nano .env
```

重要环境变量：
- `SQL_DSN`: 数据库连接字符串（默认使用SQLite）
- `REDIS_CONN_STRING`: Redis连接字符串（可选）
- `SESSION_SECRET`: 会话密钥（生产环境必须设置）
- `GIN_MODE`: 运行模式（release或debug）

### 3. 启动服务

```bash
# 方法一：一键启动（构建前端并运行后端）
make all

# 方法二：分别启动
make build-frontend  # 构建前端
make start-backend   # 启动后端

# 方法三：直接运行Go程序
go run main.go
```

## 开发环境

### 后端开发

```bash
# 运行后端服务
go run main.go

# 运行测试
go test ./...
```

### 前端开发

```bash
# 进入前端目录
cd web

# 安装依赖
bun install

# 启动开发服务器
bun run dev

# 构建生产版本
bun run build

# 代码格式化检查
bun run lint

# 代码格式化修复
bun run lint:fix
```

## 数据库配置

### SQLite（默认）
无需额外配置，数据存储在 `one-api.db` 文件中。

### MySQL
设置环境变量：
```bash
SQL_DSN="用户名:密码@tcp(主机:端口)/数据库名?charset=utf8mb4&parseTime=True&loc=Local"
```

### PostgreSQL
设置环境变量：
```bash
SQL_DSN="postgres://用户名:密码@主机:端口/数据库名?sslmode=disable"
```

## Docker部署

### Docker Compose
```bash
docker-compose up -d
```

### 直接使用Docker镜像

SQLite版本：
```bash
docker run --name new-api -d --restart always -p 3000:3000 -e TZ=Asia/Shanghai -v /home/<USER>/data/new-api:/data calciumion/new-api:latest
```

MySQL版本：
```bash
docker run --name new-api -d --restart always -p 3000:3000 -e SQL_DSN="root:123456@tcp(localhost:3306)/oneapi" -e TZ=Asia/Shanghai -v /home/<USER>/data/new-api:/data calciumion/new-api:latest
```

## 访问应用

服务启动后，可通过以下地址访问：

- 前端界面: http://localhost:3000
- API接口: http://localhost:3000/v1

默认管理员账户：
- 用户名: root
- 密码: 123456

首次登录后请立即修改默认密码。

## 常见问题

### 1. 端口冲突
修改环境变量 `PORT` 或在 `.env` 文件中设置：
```bash
PORT=3001
```

### 2. 会话问题（多机部署）
多机部署时必须设置 `SESSION_SECRET` 环境变量。

### 3. Redis配置
如需使用Redis缓存，设置 `REDIS_CONN_STRING` 环境变量：
```bash
REDIS_CONN_STRING="redis://localhost:6379"
```

## 生产环境建议

1. 设置强密码和密钥
2. 配置HTTPS
3. 设置适当的环境变量
4. 定期备份数据库
5. 配置日志轮转