import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Popconfirm,
  Typography,
  Banner,
  Toast,
  Empty,
  Tooltip,
  Spin,
} from '@douyinfe/semi-ui';
import {
  IconPlus,
  IconDelete,
  IconPlay,
  IconPause,
  IconRefresh,
  IconEyeOpened,
  IconEyeClosedSolid,
} from '@douyinfe/semi-icons';
import { API, showError, showInfo, showSuccess } from '../../helpers';

const { Text } = Typography;

const MultiKeyManager = ({ channelId, visible, onClose }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [keys, setKeys] = useState([]);
  const [multiKeyMode, setMultiKeyMode] = useState('random');
  const [showAddModal, setShowAddModal] = useState(false);
  const [newKey, setNewKey] = useState('');
  const [testingKeys, setTestingKeys] = useState(new Set());
  const [showKey, setShowKey] = useState(new Set()); // 用于控制显示完整密钥

  // 加载密钥列表
  const loadKeys = async () => {
    if (!channelId) return;

    setLoading(true);
    try {
      const res = await API.get(`/api/channel/${channelId}/keys`);
      if (res.data.success) {
        setKeys(res.data.data.keys || []);
        setMultiKeyMode(res.data.data.multi_key_mode || 'random');
      } else {
        showError(res.data.message || '加载密钥列表失败');
        // 如果不是多密钥渠道，显示提示信息
        if (res.data.message.includes('不是多密钥模式')) {
          Toast.info('该渠道不支持多密钥管理');
        }
      }
    } catch (error) {
      const errorMsg =
        error.response?.data?.message || error.message || '网络错误';
      showError('加载密钥列表失败: ' + errorMsg);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && channelId) {
      loadKeys();
    }
  }, [visible, channelId]);

  // 添加密钥
  const handleAddKey = async () => {
    if (!newKey.trim()) {
      showError('请输入密钥');
      return;
    }

    try {
      const res = await API.post(`/api/channel/${channelId}/keys`, {
        key: newKey.trim(),
      });

      if (res.data.success) {
        showSuccess('密钥添加成功');
        setNewKey('');
        setShowAddModal(false);
        loadKeys();
      } else {
        showError(res.data.message || '添加密钥失败');
      }
    } catch (error) {
      const errorMsg =
        error.response?.data?.message || error.message || '网络错误';
      showError('添加密钥失败: ' + errorMsg);
    }
  };

  // 更新密钥状态
  const handleUpdateKeyStatus = async (keyIndex, status) => {
    try {
      const res = await API.put(`/api/channel/${channelId}/keys/${keyIndex}`, {
        status: status,
      });

      if (res.data.success) {
        showSuccess(status === 1 ? '密钥已启用' : '密钥已禁用');
        loadKeys();
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError('更新密钥状态失败: ' + error.message);
    }
  };

  // 删除密钥
  const handleDeleteKey = async (keyIndex) => {
    try {
      const res = await API.delete(
        `/api/channel/${channelId}/keys/${keyIndex}`,
      );

      if (res.data.success) {
        showSuccess('密钥删除成功');
        loadKeys();
      } else {
        showError(res.data.message);
      }
    } catch (error) {
      showError('删除密钥失败: ' + error.message);
    }
  };

  // 测试密钥
  const handleTestKey = async (keyIndex) => {
    setTestingKeys((prev) => new Set([...prev, keyIndex]));

    try {
      const res = await API.post(
        `/api/channel/${channelId}/keys/${keyIndex}/test`,
      );

      if (res.data.success) {
        showSuccess(`密钥测试成功 (${res.data.data.test_model})`);
      } else {
        showError('密钥测试失败: ' + res.data.message);
      }
    } catch (error) {
      showError('密钥测试失败: ' + error.message);
    } finally {
      setTestingKeys((prev) => {
        const newSet = new Set(prev);
        newSet.delete(keyIndex);
        return newSet;
      });
    }
  };

  // 切换密钥显示状态
  const toggleKeyVisibility = (keyIndex) => {
    setShowKey((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(keyIndex)) {
        newSet.delete(keyIndex);
      } else {
        newSet.add(keyIndex);
      }
      return newSet;
    });
  };

  // 渲染状态标签
  const renderStatus = (status) => {
    switch (status) {
      case 1:
        return (
          <Tag color='green' shape='circle'>
            {t('已启用')}
          </Tag>
        );
      case 2:
        return (
          <Tag color='red' shape='circle'>
            {t('已禁用')}
          </Tag>
        );
      case 3:
        return (
          <Tag color='yellow' shape='circle'>
            {t('自动禁用')}
          </Tag>
        );
      default:
        return (
          <Tag color='grey' shape='circle'>
            {t('未知状态')}
          </Tag>
        );
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      render: (text, record, index) => index + 1,
    },
    {
      title: '密钥',
      dataIndex: 'key',
      render: (text, record) => (
        <Space>
          <Text
            code
            style={{
              fontFamily: 'Monaco, Consolas, monospace',
              fontSize: '12px',
              maxWidth: '300px',
              display: 'inline-block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {showKey.has(record.index) ? record.key : record.masked}
          </Text>
          <Button
            size='small'
            type='tertiary'
            icon={
              showKey.has(record.index) ? (
                <IconEyeClosedSolid />
              ) : (
                <IconEyeOpened />
              )
            }
            onClick={() => toggleKeyVisibility(record.index)}
          />
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status) => renderStatus(status),
    },
    {
      title: '操作',
      width: 200,
      render: (text, record) => (
        <Space>
          <Tooltip content={record.status === 1 ? '禁用密钥' : '启用密钥'}>
            <Button
              size='small'
              type='tertiary'
              icon={record.status === 1 ? <IconPause /> : <IconPlay />}
              onClick={() =>
                handleUpdateKeyStatus(record.index, record.status === 1 ? 2 : 1)
              }
            />
          </Tooltip>

          <Tooltip content='测试密钥'>
            <Button
              size='small'
              type='tertiary'
              icon={<IconRefresh />}
              loading={testingKeys.has(record.index)}
              onClick={() => handleTestKey(record.index)}
            />
          </Tooltip>

          <Popconfirm
            title='确定要删除这个密钥吗？'
            content='删除后无法恢复'
            onConfirm={() => handleDeleteKey(record.index)}
          >
            <Tooltip content='删除密钥'>
              <Button size='small' type='danger' icon={<IconDelete />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Modal
      title='多密钥管理'
      visible={visible}
      onCancel={onClose}
      width={800}
      footer={
        <Space>
          <Button onClick={onClose}>关闭</Button>
        </Space>
      }
    >
      <div>
        <Banner
          type='info'
          description={
            <div>
              <Text>当前多密钥模式: </Text>
              <Tag color={multiKeyMode === 'random' ? 'blue' : 'green'}>
                {multiKeyMode === 'random' ? '随机' : '轮询'}
              </Tag>
              <Text style={{ marginLeft: 16 }}>
                共 {keys.length} 个密钥， 已启用{' '}
                {keys.filter((k) => k.status === 1).length} 个
              </Text>
            </div>
          }
          style={{ marginBottom: 16 }}
        />

        <Banner
          type='warning'
          title='使用说明'
          description={
            <div>
              <ul style={{ paddingLeft: 20, margin: 0 }}>
                <li>多密钥模式下，系统会自动在多个密钥间分发请求</li>
                <li>随机模式：随机选择可用密钥；轮询模式：按顺序使用密钥</li>
                <li>禁用的密钥不会被使用，可以用于临时停用有问题的密钥</li>
                <li>建议定期测试密钥确保其可用性</li>
                <li>删除密钥前请确认该密钥不再需要使用</li>
              </ul>
            </div>
          }
          style={{ marginBottom: 16 }}
        />

        <Card
          title={
            <Space>
              <Text strong>密钥列表</Text>
              <Button
                size='small'
                type='primary'
                icon={<IconPlus />}
                onClick={() => setShowAddModal(true)}
              >
                添加密钥
              </Button>
            </Space>
          }
          style={{ marginBottom: 16 }}
        >
          <Spin spinning={loading}>
            {keys.length === 0 ? (
              <Empty
                image={<IconRefresh size='extra-large' />}
                title='暂无密钥'
                description='点击添加密钥按钮开始添加'
              />
            ) : (
              <Table
                columns={columns}
                dataSource={keys}
                pagination={false}
                rowKey='index'
                size='small'
              />
            )}
          </Spin>
        </Card>

        {/* 添加密钥弹窗 */}
        <Modal
          title='添加密钥'
          visible={showAddModal}
          onCancel={() => {
            setShowAddModal(false);
            setNewKey('');
          }}
          onOk={handleAddKey}
          okText='添加'
          cancelText='取消'
        >
          <Form>
            <Form.TextArea
              field='key'
              label='密钥内容'
              placeholder='请输入新的密钥'
              value={newKey}
              onChange={setNewKey}
              autosize
              rules={[{ required: true, message: '请输入密钥' }]}
            />
          </Form>
        </Modal>
      </div>
    </Modal>
  );
};

export default MultiKeyManager;
