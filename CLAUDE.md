# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

New API是一个基于Go和React开发的大模型网关与AI资产管理系统，是One API的二次开发项目。它提供统一的API接口来管理和分发多个AI服务商的模型服务。

### 核心特性
- 多模型支持：支持OpenAI、Anthropic Claude、Google Gemini、百度文心一言等多种主流AI模型
- 渠道管理：可配置不同服务商的API渠道，支持多Key轮询和权重分配
- 计费系统：支持按Token计费、按次数计费、额度管理、在线充值等功能
- 多语言支持：前后端均支持中英文界面
- 异步任务：支持Midjourney、Suno等异步任务处理
- 缓存机制：支持Redis和内存缓存，提高系统性能
- 重试机制：请求失败时自动重试其他可用渠道

## 项目架构

### 后端架构 (Go)
- **主框架**: Gin Web框架
- **数据库**: GORM ORM，支持SQLite、MySQL、PostgreSQL
- **缓存**: Redis和内存缓存
- **主要组件**:
  - `main.go`: 程序入口点
  - `router/`: 路由定义，分为API路由、中继路由、Web路由等
  - `controller/`: 控制器层，处理HTTP请求
  - `model/`: 数据模型层，定义数据库表结构和操作
  - `relay/`: 中继处理层，负责转发请求到不同AI服务商
  - `service/`: 业务逻辑层
  - `middleware/`: 中间件层，处理认证、限流等
  - `common/`: 公共工具和配置
  - `dto/`: 数据传输对象

### 前端架构 (React)
- **主框架**: React 18
- **UI库**: Semi Design组件库
- **状态管理**: React Context API
- **路由**: React Router v6
- **主要目录结构**:
  - `src/App.js`: 路由配置入口
  - `src/pages/`: 页面组件
  - `src/components/`: 可复用组件
  - `src/helpers/`: 工具函数
  - `src/context/`: 状态管理
  - `src/hooks/`: 自定义Hook

## 开发命令

### 后端开发
```bash
# 运行后端服务 (开发模式)
go run main.go

# 构建后端
go build -o new-api

# 运行测试
go test ./...
```

### 前端开发
```bash
# 安装依赖
cd web && bun install

# 启动开发服务器
cd web && bun run dev

# 构建生产版本
cd web && bun run build

# 代码格式化检查
cd web && bun run lint

# 代码格式化修复
cd web && bun run lint:fix
```

### 全栈开发
```bash
# 构建前端并启动后端 (通过makefile)
make all

# 或分别运行
make build-frontend
make start-backend
```

### Docker部署
```bash
# 使用Docker Compose部署
docker-compose up -d

# 直接使用Docker镜像 (SQLite)
docker run --name new-api -d --restart always -p 3000:3000 -e TZ=Asia/Shanghai -v /home/<USER>/data/new-api:/data calciumion/new-api:latest

# 直接使用Docker镜像 (MySQL)
docker run --name new-api -d --restart always -p 3000:3000 -e SQL_DSN="root:123456@tcp(localhost:3306)/oneapi" -e TZ=Asia/Shanghai -v /home/<USER>/data/new-api:/data calciumion/new-api:latest
```

## 核心数据模型

### Channel (渠道模型)
- 管理不同AI服务商的API接入配置
- 支持多Key轮询、权重分配、模型映射等功能
- 可配置状态码映射、基础URL、测试模型等

### User (用户模型)
- 管理系统用户账户
- 支持配额管理、角色权限控制

### Token (令牌模型)
- API访问令牌管理
- 支持分组、模型限制、IP限制等

### Log (日志模型)
- 记录API调用日志
- 用于计费统计和审计

## 请求处理流程

1. **路由匹配**: 根据请求路径匹配对应的处理函数
2. **身份验证**: 通过TokenAuth中间件验证API令牌
3. **渠道分发**: 通过Distribute中间件选择合适的渠道
4. **请求转发**: 使用适配器模式将请求转发到具体的AI服务商
5. **响应处理**: 处理响应并返回给客户端
6. **计费记录**: 记录使用日志并扣除相应配额

## 适配器模式

项目使用适配器模式支持不同AI服务商：
- `relay/channel/openai/`: OpenAI适配器
- `relay/channel/claude/`: Anthropic Claude适配器
- `relay/channel/gemini/`: Google Gemini适配器
- `relay/channel/zhipu/`: 智谱清言适配器
- 等等...

每个适配器实现统一的Adaptor接口，确保请求和响应格式的一致性。

## 缓存机制

- **内存缓存**: 用于缓存渠道信息、用户信息等频繁访问的数据
- **Redis缓存**: 用于分布式部署时的数据共享
- **自动同步**: 定时同步数据库中的配置信息到缓存

## 重试机制

- 支持配置重试次数
- 自动切换到其他可用渠道
- 根据错误类型决定是否重试

## 数据库设计

使用GORM ORM管理数据库：
- 支持SQLite、MySQL、PostgreSQL
- 自动迁移表结构
- 支持读写分离配置