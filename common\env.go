package common

import (
	"fmt"
	"os"
	"strconv"
)

// 日志数据记录配置
var (
	RecordPromptDataEnabled     bool
	RecordCompletionDataEnabled bool
	MaxPromptDataLength         int
	MaxCompletionDataLength     int
)

func InitLogDataConfig() {
	RecordPromptDataEnabled = GetEnvOrDefaultBool("RECORD_PROMPT_DATA", false)
	RecordCompletionDataEnabled = GetEnvOrDefaultBool("RECORD_COMPLETION_DATA", false)
	MaxPromptDataLength = GetEnvOrDefault("MAX_PROMPT_DATA_LENGTH", 10240)         // 默认10KB
	MaxCompletionDataLength = GetEnvOrDefault("MAX_COMPLETION_DATA_LENGTH", 10240) // 默认10KB
}

func GetEnvOrDefault(env string, defaultValue int) int {
	if env == "" || os.Getenv(env) == "" {
		return defaultValue
	}
	num, err := strconv.Atoi(os.Getenv(env))
	if err != nil {
		SysError(fmt.Sprintf("failed to parse %s: %s, using default value: %d", env, err.Error(), defaultValue))
		return defaultValue
	}
	return num
}

func GetEnvOrDefaultString(env string, defaultValue string) string {
	if env == "" || os.Getenv(env) == "" {
		return defaultValue
	}
	return os.Getenv(env)
}

func GetEnvOrDefaultBool(env string, defaultValue bool) bool {
	if env == "" || os.Getenv(env) == "" {
		return defaultValue
	}
	b, err := strconv.ParseBool(os.Getenv(env))
	if err != nil {
		SysError(fmt.Sprintf("failed to parse %s: %s, using default value: %t", env, err.Error(), defaultValue))
		return defaultValue
	}
	return b
}
